# 微信浏览器接口登录控制修改说明

## 修改概述
针对微信浏览器环境，对以下接口函数进行了登录状态控制，确保只有在用户登录后才会请求这些接口，并在登录成功后自动刷新当前页面数据。

## 涉及的接口
1. `/api/supplier/getSupplierApplyStatus` - 供应商申请状态
2. `/api/smallShop/center/showCenter` - 小商店中心显示
3. `/api/agent/setting/get` - 代理商设置获取
4. `/api/center/index` - 用户中心首页数据
5. `/api/center/getMustTools` - 必备工具获取

## 修改内容

### 1. 添加登录状态检查函数
在 `membercenter.vue` 中添加了 `needLoginForWechat()` 函数：

```javascript
// 检查是否需要登录后才请求接口（仅针对微信浏览器）
needLoginForWechat() {
  return this.checkWenxin() && this.checkNull(uni.getStorageSync('user'))
}
```

### 2. 修改的函数列表

#### 2.1 getAgentSetting() - 获取代理商基础设置
```javascript
async getAgentSetting() {
  // 微信浏览器未登录时不请求
  if (this.needLoginForWechat()) {
    return
  }
  
  const res = await this.get('/api/agent/setting/get')
  // ... 原有逻辑
}
```

#### 2.2 getShowCenter() - 是否显示小商店按钮
```javascript
getShowCenter() {
  // 微信浏览器未登录时不请求
  if (this.needLoginForWechat()) {
    return
  }
  
  this.get('/api/smallShop/center/showCenter', {}, true, false, false)
  // ... 原有逻辑
}
```

#### 2.3 indexData() - 用户信息
```javascript
indexData() {
  let api = ''
  // 用户信息
  if (uni.getStorageSync('memberKey') === 'order') {
    api = '/api/center/index'
  }
  
  // 微信浏览器未登录时不请求
  if (this.needLoginForWechat() || !api) {
    return
  }
  
  this.post(api, {}, true)
  // ... 原有逻辑
}
```

#### 2.4 supplierStatus() - 供应商状态
```javascript
supplierStatus() {
  // 微信浏览器未登录时不请求
  if (this.needLoginForWechat()) {
    return
  }
  
  this.get('/api/supplier/getSupplierApplyStatus', {}, true, true)
  // ... 原有逻辑
}
```

#### 2.5 getMustTools() - 获取必备工具
```javascript
async getMustTools() {
  // 微信浏览器未登录时不请求
  if (this.needLoginForWechat()) {
    return
  }
  
  let res = await this.post('/api/center/getMustTools')
  // ... 原有逻辑
}
```

### 3. 登录成功后页面刷新机制

#### 3.1 修改 main.js 中的登录成功逻辑
```javascript
} else if (data.code == 0) {
  // 登录成功
  that.clearWeChatCode()
  uni.setStorageSync('token', data.token)
  uni.setStorageSync('user', data.user)
  uni.setStorageSync('type', 'refresh')
  uni.showToast({
    title: res.msg,
    icon: 'none',
    duration: 2000,
  })
  setTimeout(() => {
    // 检查当前页面是否为会员中心页面
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    if (currentPage && currentPage.route === 'pages/membercenter/membercenter') {
      // 如果在会员中心页面，刷新当前页面数据
      uni.showToast({
        title: '登录成功，正在刷新数据...',
        icon: 'none',
        duration: 1000,
      })
      // 触发页面数据刷新
      setTimeout(() => {
        // 通过事件总线或直接调用页面方法刷新数据
        uni.$emit('wechatLoginSuccess')
        // 也可以通过重新加载页面的方式
        window.location.reload()
      }, 1000)
    } else {
      // 其他页面跳转到首页
      uni.switchTab({
        url: '/pages/index/index',
      })
    }
  }, 1500)
}
```

#### 3.2 添加事件监听和数据刷新函数
在 `membercenter.vue` 中：

```javascript
mounted() {
  uni.setStorageSync('memberKey', 'order')
  this.indexData()
  
  // 监听微信登录成功事件
  uni.$on('wechatLoginSuccess', () => {
    this.refreshPageData()
  })
},

beforeDestroy() {
  // 移除事件监听
  uni.$off('wechatLoginSuccess')
},

// 刷新页面数据（微信登录成功后调用）
refreshPageData() {
  console.log('微信登录成功，刷新会员中心数据')
  // 更新用户信息
  this.user = uni.getStorageSync('user')
  this.checkShow = !this.checkNull(this.user)
  
  // 重新请求所有需要登录的接口
  this.indexData()
  this.getShowCenter()
  this.supplierStatus()
  this.getMustTools()
  this.getAgentSetting()
}
```

## 实现效果

### 1. 未登录状态（仅微信浏览器）
- 页面加载时，上述5个接口不会被调用
- 避免了不必要的网络请求和错误提示
- 页面显示基础内容，等待用户登录

### 2. 登录成功后
- 自动检测当前是否在会员中心页面
- 如果在会员中心，触发数据刷新而不是页面跳转
- 重新调用所有需要登录的接口获取最新数据
- 更新页面显示状态

### 3. 非微信浏览器
- 保持原有逻辑不变
- 不受此次修改影响

## 注意事项

1. **仅针对微信浏览器**：修改只在微信浏览器环境中生效
2. **保持向后兼容**：非微信浏览器的行为保持不变
3. **事件清理**：在页面销毁时正确移除事件监听
4. **双重刷新机制**：既有事件触发刷新，也有页面重载作为备选

## 调试功能

为了便于调试，在所有相关函数中添加了详细的控制台日志输出：

```javascript
// 检查是否需要登录后才请求接口（仅针对微信浏览器）
needLoginForWechat() {
  const isWechat = this.checkWenxin()
  const user = uni.getStorageSync('user')
  const isUserNull = this.checkNull(user)
  const needLogin = isWechat && isUserNull

  console.log('needLoginForWechat 检查:', {
    isWechat,
    user,
    isUserNull,
    needLogin
  })

  return needLogin
}
```

每个受控函数都会输出：
- 函数被调用的日志
- 是否被阻止的日志
- 是否继续执行API请求的日志

## 测试建议

1. **在微信浏览器中访问会员中心页面（未登录状态）**
   - 打开浏览器开发者工具的控制台
   - 访问会员中心页面
   - 查看控制台日志，应该看到类似输出：
     ```
     needLoginForWechat 检查: {isWechat: true, user: "", isUserNull: true, needLogin: true}
     getAgentSetting 被调用
     getAgentSetting 被阻止 - 微信浏览器未登录
     getShowCenter 被调用
     getShowCenter 被阻止 - 微信浏览器未登录
     indexData 被调用
     indexData 被阻止 - 微信浏览器未登录或无API
     supplierStatus 被调用
     supplierStatus 被阻止 - 微信浏览器未登录
     getMustTools 被调用
     getMustTools 被阻止 - 微信浏览器未登录
     ```

2. **检查网络请求**
   - 在开发者工具的Network标签页中
   - 确认上述5个接口未被调用

3. **进行微信登录**
   - 完成微信登录流程
   - 查看控制台日志，应该看到：
     ```
     微信登录成功，刷新会员中心数据
     needLoginForWechat 检查: {isWechat: true, user: "{...}", isUserNull: false, needLogin: false}
     indexData 被调用
     indexData 继续执行 - 发起API请求: /api/center/index
     getShowCenter 被调用
     getShowCenter 继续执行 - 发起API请求
     ...
     ```

4. **确认登录成功后页面数据正确刷新**
   - 检查页面内容是否正确显示用户信息
   - 确认相关功能模块正常显示

5. **在非微信浏览器中测试**
   - 使用Chrome、Safari等非微信浏览器访问
   - 确保原有功能正常，不受此次修改影响
   - 控制台应该显示：
     ```
     needLoginForWechat 检查: {isWechat: false, user: "", isUserNull: true, needLogin: false}
     ```

## 移除调试日志

在生产环境部署前，建议移除所有 `console.log` 调试语句，或者使用条件编译只在开发环境中输出。
