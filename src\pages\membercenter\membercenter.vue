<!-- 会员中心 -->
<template>
  <view class="u-page">
    <view class="head">
      <view class="head_my d-f">
        <view class="user-info-img">
          <u-avatar
            :src="userInfo.avatar"
            size="110rpx"
            shape="circle"
          ></u-avatar>
        </view>

        <view class="my_name">
          <view v-if="(isMPWEIXIN && checkNull(user)) || !isMPWEIXIN">
            <view class="font_size16 c-white mt-15">
              {{ userInfo.nickname || '普通用户' }}
            </view>
            <view v-if="checkNull(user)">
              <view class="d-f font_size12 c-white">
                邀请码:{{ userInfo.invite_code }}
                <view class="my_copy c-orange bg-white" @tap="myCopy">
                  复制
                </view>
              </view>
              <view class="mt-20 d-cf">
                <view
                  class="my_member"
                  @click="navTo('/packageD/memberLevel/memberRight')"
                >
                  <image
                    class="my_member_img"
                    src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/img/level-icon.png"
                  ></image>
                  <text>{{ levelName }}</text>
                  <view class="fa-angle-right"></view>
                </view>
                <view
                  class="ml-20 font_size12 c-white"
                  @click="navTo('/packageD/memberLevel/memberRight')"
                >
                  <view class="con-arrow" v-if="userInfo.validity">
                    有效期:{{ userInfo.validity }}
                    <u-icon name="arrow-right"></u-icon>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view
            style="background-color: #f2c7a0; width: 80rpx; height: 50rpx"
            class="radius10 fs-1 d-cc mt-30"
            @click="navTo('/pages/login/login')"
            v-if="!checkNull(user)"
          >
            登录
          </view>
          <!-- #ifdef MP-WEIXIN -->
          <!-- 模态弹窗 -->
          <u-modal
            :show="checkShow"
            :showCancelButton="true"
            confirmColor="#35ba89"
            confirmText="微信授权"
            width="500rpx"
            @confirm="
              miniLogin()
              checkShow = false
            "
            @cancel="checkShow = false"
          >
            <view>
              <view class="fs-4">欢迎来到{{ tabTitle }}</view>
              <view class="fs-2 mt-40">登录尽享各种优化权益！</view>
            </view>
          </u-modal>
          <!-- #endif -->
        </view>
        <view
          v-if="personalIsShow"
          class="iconfont icon-massage_set"
          @click="navTo('/packageB/user/personalData/personalData')"
        ></view>
      </view>

      <view class="u-demo-block__content">
        <u-row
          customStyle="margin-top:26rpx;height: 130rpx;background:#fff;border-radius:10rpx;"
        >
          <u-col
            span="6"
            @click="navTo('/packageB/user/collect/collect?type=commodity')"
          >
            <view class="demo-layout d-cc-c f-bold">
              <view class="mb-20 font_size13">
                {{
                  userInfo.favorite_product_num
                    ? userInfo.favorite_product_num
                    : 0
                }}
              </view>
              <view class="c-gray">收藏商品</view>
            </view>
          </u-col>
          <u-col
            span="6"
            @click="navTo('/packageB/user/collect/collect?type=store')"
          >
            <view class="demo-layout d-cc-c f-bold">
              <view class="mb-20 font_size13">
                {{
                  userInfo.favorite_supplier_num
                    ? userInfo.favorite_supplier_num
                    : 0
                }}
              </view>
              <view class="c-gray">收藏店铺</view>
            </view>
          </u-col>
        </u-row>
      </view>
    </view>
    <view class="my_content">
      <view
        class="d-cf grade_suppliers mb-20"
        v-if="supplierIsShow && !status && !isApply"
      >
        <image
          src="https://mini-app-img-1251768088.cos.ap-guangzhou.myqcloud.com/uni-supply-platform/Upgrade.png"
          class="grade_img"
        ></image>
        <view class="font_size13 c-grayF f-bold flex_max">升级成为供应商</view>
        <view
          class="suppliers_btn font_size11 c-grayF"
          @click="navTo('/packageB/member/supplier')"
        >
          去申请
        </view>
      </view>
      <view v-else class="mb-20"></view>

      <!-- 切换tabs -->
      <view class="my_order">
        <view class="order_title font_size14 f-bold mb-5">
          <u-tabs
            class="orderTabs"
            lineWidth="30"
            lineHeight="5"
            lineColor="#FF6666"
            @change="changeOrderId"
            :list="orderMenu"
            :activeStyle="{
              color: '#191919',
              fontWeight: 'bold',
              transform: 'scale(1.01)',
            }"
            :inactiveStyle="{
              color: '#101010',
              transform: 'scale(0.9)',
            }"
          ></u-tabs>
        </view>
        <u-line></u-line>
        <!--我的订单 -->
        <my-order v-if="orderId == 0" :orderList="orderList"></my-order>
        <!-- 蛋糕订单 -->
        <my-order v-if="orderId == 1" :orderList="cakeList"></my-order>
        <!-- 电影票订单 -->
        <my-order v-if="orderId == 2" :orderList="cinemaList"></my-order>
        <!-- 数字权益商品订单 -->
        <my-order v-if="orderId == 3" :orderList="fuluList"></my-order>
      </view>

      <!--我的钱包 -->
      <view class="my_wallet mt-20 mb-20 b-r-10 d-f">
        <view class="wallet_left">
          <scroll-view
            class="scroll-view_H"
            scroll-x="true"
            scroll-left="120"
            :scroll-left="scroll_left"
            scroll-with-animation="true"
          >
            <view class="d-bf">
              <!-- <view v-if="join_balance === '1'" class="wallet_box d-cc-c f-bold">
                <view class="mb-10 font_size13" v-if="userInfo.balance">
                  {{ userInfo.balance }}
                </view>
                <view v-else class="mb-10 font_size13">0.00</view>
                <view class="c-gray">汇聚余额</view>
              </view> -->
              <view class="wallet_box d-cc-c f-bold">
                <view class="mb-10 font_size13" v-if="userInfo.goin_balance">
                  {{ userInfo.goin_balance }}
                </view>
                <view v-else class="mb-10 font_size13">0.00</view>
                <view class="c-gray">站内余额</view>
              </view>
              <view class="wallet_box d-cc-c f-bold">
                <view class="mb-10 font_size13" v-if="userInfo.sett_balance">
                  {{ userInfo.sett_balance }}
                </view>
                <view v-else class="mb-10 font_size13">0.00</view>
                <view class="c-gray">站内结算余额</view>
              </view>
              <view class="wallet_box d-cc-c f-bold">
                <view class="mb-10 font_size13" v-if="userInfo.user_income">
                  {{ userInfo.user_income }}
                </view>
                <view v-else class="mb-10 font_size13">0.00</view>
                <view class="c-gray">收入</view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view
          class="wallet_right d-cc-c"
          @click="navTo('/packageB/wallet/wallet')"
        >
          <view class="mb-10 iconfont icon-fontclass-shouru"></view>
          <view class="c-gray">我的钱包</view>
        </view>
      </view>

      <!--会员工具分类 -->
      <my-beauty :mYtitle="tool" :mylist="toolList" ref="myBeautys"></my-beauty>
      <!-- :apply_status="apply_status" :tip="tip" -->
      <!-- 店铺管理 -->
      <my-beauty
        v-if="!showSmallShop"
        :mYtitle="purchase"
        :mylist="pList"
        :isShowShop="isShowShop"
        ref="myBeautys"
      ></my-beauty>
      <my-small
        v-else
        ref="mySmall"
        :mYtitle="purchase"
        :mylist="smallList"
      ></my-small>
      <!-- 本地生活 -->
      <local-life v-if="localLifeShow"></local-life>
      <!-- 营销工具 -->
      <my-beauty
        mYtitle="营销工具"
        :mylist="marketingtool"
        ref="myBeautys"
      ></my-beauty>
      <view
        class="loginOut"
        @click="$clicks(loginOut)"
        v-if="this.checkNull(user)"
      >
        退出登录
      </view>

      <u-modal
        :show="loginShow"
        :content="loginContent"
        @confirm="loginConfirm"
      ></u-modal>
    </view>
    <myTabBar ref="myTabBar"></myTabBar>
  </view>
</template>

<script>
import myOrder from '@/common/myOrder/myOrder.vue'
import myBeauty from '@/common/myBeauty/myBeauty.vue'
import mySmall from '@/common/mySmall/mySmall.vue'
import myTabBar from '../../components/tabbar.vue'
import localLife from '@/common/localLife/localLife.vue'
import { applyStatus } from '@/mixin/applyStatus'
export default {
  mixins: [applyStatus],
  components: {
    myOrder,
    myBeauty,
    mySmall,
    myTabBar,
    localLife,
  },
  data() {
    return {
      orderMenu: [
        {
          name: '我的订单',
        },
      ],
      orderId: 0, // 订单列表 0-我的订单 1-蛋糕订单 2-电影票订单 3-数字权益商品订单
      applyStatus: 1, // 小商店状态
      showSmallShop: false, // 是否显示小商店列表
      supplierIsShow: false, // 供应商入口是否显示(后台开关)
      personalIsShow: false,
      scroll_left: 0, // 精选臻品 nav 栏的位置
      userInfo: {},
      join_balance: '', // 汇聚余额显示
      status: '',
      // apply_status: 1, // 小商店状态：1-可申请
      // tip: '',
      isShowShop: 0, // 是否显示小商店按钮
      user: '', // 用户信息
      isApply: '',
      isMPWEIXIN: false, // 是否为微信小程序端
      loginShow: false,
      checkShow: false,
      tabTitle: '',
      loginContent: '退出成功',
      levelName: '', // 会员名称
      userOrder: {},
      src: 'https://dev3.yunzmall.com/attachment/image/3ab8eb21877ba0e6a5c45d89e9029aa2.jpeg',
      tool: '必备工具',
      purchase: '店铺管理',
      channel: '渠道商中心',
      orderList: [
        {
          nameClass: 'icon-fontclass-daifukuan',
          title: '待付款',
          navigation: '/packageA/myOrder/myOrder?status=0',
          code: 'wait_pay_num',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daifahuo',
          title: '待发货',
          navigation: '/packageA/myOrder/myOrder?status=1',
          code: 'wait_send_num', // 数量的名称
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daishouhuo1',
          title: '待收货',
          navigation: '/packageA/myOrder/myOrder?status=2',
          code: 'wait_receive_num',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-shouhouliebiao',
          title: '售后列表',
          navigation: '/packageB/member/aftersaleslist',
        },
        {
          nameClass: 'icon-fontclass-quanbudingdan',
          title: '全部订单',
          navigation: '/packageA/myOrder/myOrder',
          num: 0,
        },
      ],
      cakeList: [
        {
          nameClass: 'icon-fontclass-daifukuan',
          title: '待支付',
          navigation: '/packageA/myOrder/myOrder?status=0',
          code: 'WaitPayNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daifahuo',
          title: '待发货',
          navigation: '/packageA/myOrder/myOrder?status=1',
          code: 'WaitSendNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daishouhuo1',
          title: '待收货',
          navigation: '/packageA/myOrder/myOrder?status=2',
          code: 'WaitReceiveNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-shouhouliebiao',
          title: '退换货',
          navigation: '/packageA/myOrder/myOrder?status=5',
          code: 'BackNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-quanbudingdan',
          title: '全部订单',
          navigation: '/packageA/myOrder/myOrder',
          num: 0,
        },
      ],
      cinemaList: [
        {
          nameClass: 'icon-fontclass-daifukuan',
          title: '待付款',
          navigation: '/packageA/myOrder/myOrder?status=10',
          code: 'WaitPayNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daifahuo',
          title: '待接单',
          navigation: '/packageA/myOrder/myOrder?status=0',
          code: 'WaitReceivingNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daishouhuo1',
          title: '已接单',
          navigation: '/packageA/myOrder/myOrder?status=1',
          code: 'IsReceivingNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daifukuan',
          title: '已完成',
          navigation: '/packageA/myOrder/myOrder?status=3',
          code: 'CompletedNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-quanbudingdan',
          title: '全部订单',
          navigation: '/packageA/myOrder/myOrder',
          num: 0,
        },
      ],
      fuluList: [
        {
          nameClass: 'icon-fontclass-daifukuan',
          title: '待付款',
          navigation: '/packageA/myOrder/myOrder?status=0',
          code: 'WaitPayNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daifahuo',
          title: '待发货',
          navigation: '/packageA/myOrder/myOrder?status=1',
          code: 'WaitSendNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daifahuo',
          title: '待收货',
          navigation: '/packageA/myOrder/myOrder?status=2',
          code: 'WaitReceiveNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-daishouhuo1',
          title: '已完成',
          navigation: '/packageA/myOrder/myOrder?status=3',
          code: 'CompletedNum',
          num: 0,
        },
        {
          nameClass: 'icon-fontclass-quanbudingdan',
          title: '全部订单',
          navigation: '/packageA/myOrder/myOrder',
          num: 0,
        },
      ],
      toolList: [
        {
          nameClass: 'icon-massage_client icon_blue',
          title: '我的客户',
          url: '/packageB/user/myCustomer/myCustomer',
        },
        {
          nameClass: 'icon-zx_map_locate1 icon_blue',
          title: '地址管理',
          url: '/packageB/member/address',
        },
        {
          nameClass: 'icon-fapiao icon_orange',
          title: '我的发票',
          url: '/packageC/invoice/invoice',
        },
        {
          nameClass: 'icon-fontclass-piliangxiazai icon_violet',
          title: '批量下单',
          url: '/packageC/member/bulkOrder',
        },
      ],
      marketingtool: [
        {
          nameClass: 'icon-sucaizhongxin icon_myMaterial',
          title: '素材中心',
          url: '/packageE/marketingTool/materialCenter/materialCenter',
        },
        {
          nameClass: 'icon-dailishang icon_green',
          title: '代理商',
          url: '/packageE/agent/applyAgent',
        },
      ],
      shopTRUE: '',
      purchaseList: [
        {
          nameClass: 'icon-fontclass-dianpu1 icon_violet',
          title: '申请开店',
          // url: '/packageD/smallShop/smallShopManage',
        },
      ],
      smallList: [
        {
          nameClass: 'icon-a-ht_basis_shangpin_huaban1_huaban1',
          title: '商品管理',
          url: '/packageD/smallShop/productManage',
        },
        {
          nameClass: 'icon-cengji',
          title: '专辑管理',
          url: '/packageD/smallShop/albumManage',
        },
        {
          nameClass: 'icon-shanghu',
          title: '店铺设置',
          url: '/packageD/smallShop/storeSetting',
        },
        {
          nameClass: 'icon-xuanpin',
          title: '在线选品',
          url: '/packageD/smallShop/productSelection',
        },
        {
          nameClass: 'icon-kecheng',
          title: '课程管理',
          url: '/packageD/smallShop/courseManage',
        },
        {
          nameClass: 'icon-zhibo',
          title: '直播管理',
          url: '/packageD/smallShop/liveStreamManage',
        },
        {
          nameClass: 'icon-xiaodianhuiyuan',
          title: '小店会员',
          url: '/packageD/smallShop/allMember',
        },
      ],
      userTrue: false,
      pid: 0,
      localLifeShow: false,
    }
  },
  computed: {
    pList() {
      let list
      if (this.isShowShop !== 1) {
        list = []
      } else {
        list = this.purchaseList
      }
      return list
    },
  },
  onLoad(e) {
    // 二维码携带参数传入
    if (e.pid) {
      this.userTrue = true
      this.pid = e.pid
      this.post('/api/institution/getIdentity', { identity_type: 3 }).then(
        res => {
          if (res.code === 0) {
            if (res.data.identity_res) {
              this.navTo('/packageD/smallShop/smallShopManage')
            } else {
              this.$nextTick(() => {
                uni.setStorageSync('smShop', 'smShop')
                this.$refs.myBeautys.popShow = true
              })
            }
          }
        },
      )
    }
    // #ifdef MP-WEIXIN
    this.isMPWEIXIN = true
    // #endif
    this.getPersonalIsShow()
    this.getSupplierIsshow()
    // 以下接口在微信浏览器未登录时不调用
    if (!this.needLoginForWechat()) {
      this.supplierStatus()
      this.getMustTools()
      this.getAgentSetting() // 代理商是否显示
    }
    // this.getApplyStatus()
    this.getAuthorizedPlugins() // 判断有无应用权限
    this.getVideoIsShow() // 发现视频是否展示
    // this.getAgentCheck() // 会员是否是代理商
  },
  onShow() {
    // 保存本地存储状态
    if (!uni.getStorageSync('memberKey')) {
      uni.setStorageSync('memberKey', 'order')
    }
    this.$nextTick(() => {
      // this.$refs.myTabBar.tabBarActive = this.$store.state.agentShow ? 4 : 3
      this.$refs.myTabBar.tabBarActive = uni.getStorageSync('tabBarActive')
    })
    this.user = uni.getStorageSync('user')

    // 以下接口在微信浏览器未登录时不调用
    if (!this.needLoginForWechat()) {
      this.getShowCenter()
      this.indexData()
    }

    if (this.userTrue && !this.checkNull(this.user)) {
      uni.setStorageSync('invite_code', this.pid)
      this.navTo(`/pages/login/login`) // 扫码判断是否为登入状态
    }
    this.tabTitle = uni.getStorageSync('tabTitle')
    if (this.checkWenxin() && !this.checkNull(this.user)) {
      this.get('/api/wechatofficial/isOpenWechat').then(({ code, data }) => {
        if (code === 0 && data.is_open === 1) {
          // 公众号登录
          this.isWeiXinBrowser()
        } else {
          this.navTo(`/pages/login/login`)
        }
      })
    }
    if (!this.checkNull(this.user)) {
      this.checkShow = true
    } else {
      this.checkShow = false
    }
    this.getIsShowSetting()
  },
  mounted() {
    uni.setStorageSync('memberKey', 'order')

    // 微信浏览器未登录时不调用indexData
    if (!this.needLoginForWechat()) {
      this.indexData()
    }

    // 监听微信登录成功事件
    uni.$on('wechatLoginSuccess', () => {
      this.refreshPageData()
    })
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('wechatLoginSuccess')
  },
  methods: {
    // 检查是否需要登录后才请求接口（仅针对微信浏览器）
    needLoginForWechat() {
      const isWechat = this.checkWenxin()
      const user = uni.getStorageSync('user')
      const isUserNull = this.checkNull(user)
      const needLogin = isWechat && isUserNull

      console.log('needLoginForWechat 检查:', {
        isWechat,
        user,
        isUserNull,
        needLogin
      })

      return needLogin
    },

    // 刷新页面数据（微信登录成功后调用）
    refreshPageData() {
      console.log('微信登录成功，刷新会员中心数据')
      // 更新用户信息
      this.user = uni.getStorageSync('user')
      this.checkShow = !this.checkNull(this.user)

      // 重新请求所有需要登录的接口
      this.indexData()
      this.getShowCenter()
      this.supplierStatus()
      this.getMustTools()
      this.getAgentSetting()
    },

    // 获取代理商基础设置
    async getAgentSetting() {
      console.log('getAgentSetting 被调用')
      // 微信浏览器未登录时不请求
      if (this.needLoginForWechat()) {
        console.log('getAgentSetting 被阻止 - 微信浏览器未登录')
        return
      }
      console.log('getAgentSetting 继续执行 - 发起API请求')

      const res = await this.get('/api/agent/setting/get')
      if (res.code === 0) {
        if (res.data.setting.value.base_setting.switch === 1) {
          await this.getAgentCheck()
        } else {
          this.marketingtool = this.marketingtool.filter(
            item => item.title !== '代理商',
          )
        }
        if (res.data.setting.value.base_setting.custom_plugin_name !== '') {
          this.marketingtool.forEach(item => {
            if (item.title === '代理商') {
              item.title =
                res.data.setting.value.base_setting.custom_plugin_name
            }
          })
        }
      }
    },
    // 会员是否是代理商
    async getAgentCheck() {
      const res = await this.post('/api/agent/check', {}, false, false, false)
      if (res.code === 0) {
        this.marketingtool.forEach(item => {
          if (item.title === '代理商') {
            item.url = '/packageE/agent/agentHome'
          }
        })
      }
    },
    async getVideoIsShow() {
      const { code, data } = await this.post('/api/video/enabled')
      if (code === 0 && data.isEnabled === true) {
        let item = {
          nameClass: 'icon-fontclass-ship icon_myVideo',
          title: '发现视频',
          url: '/packageE/marketingTool/miniVideo/miniVideo?pause=true',
        }
        this.marketingtool.unshift(item)
      }
    },
    // 获取权限判断
    async getAuthorizedPlugins() {
      const res = await this.get('/api/home/<USER>')
      if (res.code === 0) {
        res.data.forEach(item => {
          // 本地生活应用的权限
          if (item.key === 'local_life') {
            this.localLifeShow = item.enabled
          }
        })
      }
    },
    // 订单列表改变ID
    async changeOrderId(item) {
      this.orderId = item.index
      if (this.orderId === 0) {
        // 我的订单
        uni.setStorageSync('memberKey', 'order')
        this.indexData()
      } else if (this.orderId === 1) {
        uni.setStorageSync('memberKey', 'cake')
        // 蛋糕叔叔商品订单
        const res = await this.get('/api/cake/getLocalOrderList')
        if (res.code === 0) {
          const cakeData = res.data
          const cakeList = this.cakeList
          Object.keys(cakeData)
            .sort()
            .forEach(function (key, index) {
              for (const item in cakeList) {
                if (key == cakeList[item].code) {
                  cakeList[item].num = cakeData[key]
                }
              }
            })
        }
      } else if (this.orderId === 2) {
        uni.setStorageSync('memberKey', 'cinema')
        // 电影票订单
        const res = await this.post('/api/cinemaTicket/orderList')
        if (res.code === 0) {
          const cinemaData = res.data
          const cinemaList = this.cinemaList
          Object.keys(cinemaData)
            .sort()
            .forEach(function (key, index) {
              for (const item in cinemaList) {
                if (key == cinemaList[item].code) {
                  cinemaList[item].num = cinemaData[key]
                }
              }
            })
        }
      } else {
        uni.setStorageSync('memberKey', 'fulu')
        // 数字权益商品订单
        const res = await this.get('/api/fuluSupply/api/order/list')
        if (res.code === 0) {
          const fuluData = res.data
          const fuluList = this.fuluList
          Object.keys(fuluData)
            .sort()
            .forEach(function (key, index) {
              for (const item in fuluList) {
                if (key == fuluList[item].code) {
                  fuluList[item].num = fuluData[key]
                }
              }
            })
        }
      }
    },
    // 判断显示课程
    async getIsShowSetting() {
      const { data } = await this.post(
        '/api/common/smallShopOwnerAuthority',
        {},
        false,
        false,
        false,
      )
      if (data && data.length) {
        for (let i = 0; i < data.length; i++) {
          // 课程管理是否显示
          if (data[i].plugin_id !== 18 && data[i].name === '课程管理') {
            this.smallList.splice(4, 1)
            break
          }
        }
      }
    },
    // 获取小商店列表
    async getSmallList() {
      if (this.applyStatus === -1 && this.isShowShop === 1) {
        this.showSmallShop = true
        this.$refs.mySmall.getSmallShopStatistics()
      }
    },
    // 获取供应商开关是否显示
    async getSupplierIsshow() {
      const { data } = await this.get('/api/supplier/findSetting')
      this.supplierIsShow =
        data.resupplierSetting.value.is_open_supplier_entry !== 2
    },
    // 获取个人页面按钮显隐
    async getPersonalIsShow() {
      const { data } = await this.get('/api/wechatmini/getWechatMiniSetting')
      this.personalIsShow =
        data.setting.hide_personal_data_page === 1 ? false : true
    },
    // 是否显示小商店按钮
    getShowCenter() {
      console.log('getShowCenter 被调用')
      // 微信浏览器未登录时不请求
      if (this.needLoginForWechat()) {
        console.log('getShowCenter 被阻止 - 微信浏览器未登录')
        return
      }
      console.log('getShowCenter 继续执行 - 发起API请求')

      this.get('/api/smallShop/center/showCenter', {}, true, false, false)
        .then(res => {
          if (res.code === 0) {
            this.isShowShop = res.data.show
            this.applyStatus = res.data.apply_status
            this.getSmallList()
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    indexData() {
      console.log('indexData 被调用')
      let api = ''
      // 用户信息
      if (uni.getStorageSync('memberKey') === 'order') {
        api = '/api/center/index'
      }

      // 微信浏览器未登录时不请求
      if (this.needLoginForWechat() || !api) {
        console.log('indexData 被阻止 - 微信浏览器未登录或无API')
        return
      }
      console.log('indexData 继续执行 - 发起API请求:', api)

      this.post(api, {}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.join_balance = res.data.balance_settings.join_balance
            this.userInfo = data.user
            if (this.userInfo.validity !== '长期有效') {
              const end = this.userInfo.validity.indexOf(' ')
              this.userInfo.validity = this.userInfo.validity.slice(0, end)
              this.userInfo.validity = this.userInfo.validity.replace('-', '年')
              this.userInfo.validity = this.userInfo.validity.replace('-', '月')
              this.userInfo.validity = this.userInfo.validity.concat('号')
            }

            this.userInfo.sett_balance = this.toYuan(
              data.user?.sett_balance ? data.user?.sett_balance : '0.00',
            )
            this.userInfo.user_income = this.toYuan(data.user.user_income)
            this.userInfo.goin_balance = this.toYuan(data.user.goin_balance)
            this.userInfo.balance = this.toYuan(data.user.balance)
            this.userOrder = data.order
            this.levelName = this.userInfo.level?.name
            const orderData = data.order
            const orderList = this.orderList
            Object.keys(orderData)
              .sort()
              .forEach(function (key, index) {
                // 获取后台的订单数量，遍历到我的订单数组里面
                for (const item in orderList) {
                  // 遍历两次修改后台的订单数量和订单数组的索引对不上问题
                  if (key == orderList[item].code) {
                    orderList[item].num = orderData[key]
                  }
                }
              })
            // this.changeOrderId(0)
            if (data.order.is_uncle_cake_navigation === 1) {
              this.orderMenu.push({
                name: '蛋糕订单',
              })
            }
            if (data.order.is_cinema_ticket_navigation === 1) {
              this.orderMenu.push({
                name: '电影票订单',
              })
            }
            if (data.order.is_fulu_navigation === 1) {
              this.orderMenu.push({
                name: '数字权益商品订单',
              })
            }
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    supplierStatus() {
      console.log('supplierStatus 被调用')
      // 微信浏览器未登录时不请求
      if (this.needLoginForWechat()) {
        console.log('supplierStatus 被阻止 - 微信浏览器未登录')
        return
      }
      console.log('supplierStatus 继续执行 - 发起API请求')

      this.get('/api/supplier/getSupplierApplyStatus', {}, true, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.status = data.restatus
            this.isApply = data.isApply
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    myCopy() {
      // #ifdef H5
      this.$copyText(this.userInfo.invite_code)
        .then(res => {
          uni.showToast({
            title: '复制成功',
            icon: 'none',
          })
        })
        .catch(Error => {
          uni.showToast({
            title: '复制失败',
            icon: 'none',
          })
        })
      // #endif

      // #ifndef H5
      uni.setClipboardData({
        data: this.userInfo.invite_code, // e是你要保存的内容
        success: function () {
          uni.showToast({
            title: '复制成功',
            icon: 'none',
          })
        },
      })
      // #endif
    },
    loginOut() {
      this.loginShow = true
    },
    loginConfirm() {
      // 退出登录
      uni.clearStorageSync('token')
      uni.clearStorageSync('user')
      this.loginShow = false

      // 使用统一的微信授权码清理函数
      if (this.clearWeChatCode) {
        this.clearWeChatCode()
      }

      setTimeout(() => {
        uni.redirectTo({
          url: '/pages/index/index',
        })
      })
    },
    // 获取 必备工具
    async getMustTools() {
      console.log('getMustTools 被调用')
      // 微信浏览器未登录时不请求
      if (this.needLoginForWechat()) {
        console.log('getMustTools 被阻止 - 微信浏览器未登录')
        return
      }
      console.log('getMustTools 继续执行 - 发起API请求')

      let res = await this.post('/api/center/getMustTools')
      if (res.code === 0) {
        res.data.tools.forEach(element => {
          this.toolList.push({
            nameClass: element.name_class,
            nameImg: element.name_img,
            title: element.title,
            url: element.url,
          })
        })
      }
    },
  },
}
</script>
<style scoped>
.con-arrow {
  display: flex;
  flex-direction: row;
}

::v-deep .con-arrow span {
  font-size: 24rpx;
  font-weight: bold;
  color: #fff;
}

::v-deep .u-modal__content {
  text-align: center;
}
</style>
<style lang="scss">
.head {
  position: relative;
  background-image: linear-gradient(#f15353, #f5f5f5);
  padding: 42rpx 30rpx 0 30rpx;

  .head_my {
    .user-info-img {
      width: 110rpx;
      height: 110rpx;
      border-radius: 50%;
      overflow: hidden;

      image {
        width: 110rpx;
        height: 110rpx;
        border-radius: 50%;
      }
    }

    .my_name {
      flex: 2;
      // width:60%;
      margin-left: 28rpx;

      .my_copy {
        width: 68rpx;
        height: 30rpx;
        line-height: 30rpx;
        border-radius: 100rpx;
        padding: 4rpx 8rpx;
        font-size: 22rpx;
        transform: scale(0.9);
        text-align: center;
        font-family: Arial;
        margin-left: 30rpx;
      }

      .my_member {
        // max-width: 210rpx;
        height: 40rpx;
        line-height: 40rpx;
        padding: 0 30rpx 0 14rpx;
        background-color: #a36705;
        color: #ffaa29;
        border-radius: 100rpx;

        .my_member_img {
          width: 33rpx;
          height: 30rpx;
          vertical-align: middle;
          margin: 0 8rpx 8rpx 0;
        }

        text {
          font-size: 22rpx;
          transform: scale(0.9);
        }
      }
    }

    .icon_w {
      width: 20rpx;
      height: 20rpx;
    }
  }

  .icon-massage_set {
    font-size: 30rpx;
    color: #fff;
  }
}

.my_content {
  padding: 0 30rpx 0 30rpx;

  .grade_suppliers {
    height: 80rpx;
    border-radius: 10rpx;
    margin-top: 16rpx;
    background-image: linear-gradient(99deg, #f7ecd6 0%, #e9cf94 100%);

    .grade_img {
      width: 50rpx;
      height: 56rpx;
      margin: 0 17rpx 0 23rpx;
    }

    .suppliers_btn {
      width: 110rpx;
      height: 44rpx;
      line-height: 44rpx;
      margin-right: 34rpx;
      border-radius: 6rpx;
      border: solid 1px #83591f;
      font-family: SourceHanSansCN-Regular;
      font-size: 22rpx;
      font-weight: normal;
      font-stretch: normal;
      text-align: center;
    }
  }

  .my_wallet {
    background-color: #fff;
    height: 130rpx;

    .wallet_left {
      width: 76%;
      box-sizing: border-box;
      padding: 38rpx 0 30rpx 52rpx;

      .scroll-view_H {
        white-space: nowrap;

        .wallet_box {
          margin-right: 86rpx;
        }
      }
    }

    .wallet_right {
      width: 24%;
      box-sizing: border-box;
      padding: 12rpx 0;
      position: relative;

      ::after {
        content: ' ';
        position: absolute;
        left: 0;
        top: 0;
        width: 2px;
        bottom: 0;
        background-image: radial-gradient(#d4d4d4 5%, #fff 80%, #fff 0);
      }

      .iconfont {
        font-size: 26px;
        color: #f15353;
      }
    }
  }

  .loginOut {
    width: 100%;
    height: 82rpx;
    line-height: 82rpx;
    border-radius: 10rpx;
    background-color: rgba(255, 102, 102, 100);
    font-size: 28rpx;
    color: #fff;
    text-align: center;
    margin-bottom: 82rpx;
  }
}

.my_order {
  background-color: #fff;
  padding: 25rpx 30rpx 30rpx 30rpx;
}
</style>
