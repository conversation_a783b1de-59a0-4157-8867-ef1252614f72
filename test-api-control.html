<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信浏览器接口控制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .api-list {
            list-style-type: none;
            padding: 0;
        }
        .api-list li {
            background-color: #f8f9fa;
            margin: 5px 0;
            padding: 8px;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>微信浏览器接口控制测试</h1>
    <p>此页面用于测试微信浏览器中接口的登录控制功能。</p>

    <div class="test-section">
        <h2>1. 环境检测</h2>
        <button onclick="checkEnvironment()">检测当前环境</button>
        <div id="envResult"></div>
    </div>

    <div class="test-section">
        <h2>2. 受控接口列表</h2>
        <p>以下接口在微信浏览器未登录时不应该被调用：</p>
        <ul class="api-list">
            <li>/api/supplier/getSupplierApplyStatus - 供应商申请状态</li>
            <li>/api/smallShop/center/showCenter - 小商店中心显示</li>
            <li>/api/agent/setting/get - 代理商设置获取</li>
            <li>/api/center/index - 用户中心首页数据</li>
            <li>/api/center/getMustTools - 必备工具获取</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>3. 登录状态模拟</h2>
        <button onclick="simulateLogin()">模拟登录状态</button>
        <button onclick="simulateLogout()">模拟未登录状态</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h2>4. 函数测试</h2>
        <button onclick="testNeedLoginFunction()">测试needLoginForWechat函数</button>
        <div id="functionResult"></div>
    </div>

    <div class="test-section">
        <h2>5. 网络请求监控</h2>
        <button onclick="startNetworkMonitor()">开始监控网络请求</button>
        <button onclick="stopNetworkMonitor()">停止监控</button>
        <button onclick="clearNetworkLog()">清除日志</button>
        <div id="networkResult"></div>
        <div class="code-display" id="networkLog"></div>
    </div>

    <script>
        // 模拟uni对象
        const uni = {
            storage: {},
            getStorageSync: function(key) {
                return this.storage[key] || '';
            },
            setStorageSync: function(key, value) {
                this.storage[key] = value;
            },
            removeStorageSync: function(key) {
                delete this.storage[key];
            },
            showToast: function(options) {
                console.log('Toast:', options.title);
            }
        };

        // 网络请求监控
        let originalFetch = window.fetch;
        let originalXHR = window.XMLHttpRequest;
        let networkLog = [];
        let isMonitoring = false;

        // 模拟checkWenxin函数
        function checkWenxin() {
            const ua = navigator.userAgent.toLowerCase();
            return ua.indexOf('micromessenger') !== -1;
        }

        // 模拟checkNull函数
        function checkNull(value) {
            return !value || value === '' || value === null || value === undefined;
        }

        // 模拟needLoginForWechat函数
        function needLoginForWechat() {
            return checkWenxin() && checkNull(uni.getStorageSync('user'));
        }

        function checkEnvironment() {
            const resultDiv = document.getElementById('envResult');
            let results = [];

            const isWechat = checkWenxin();
            const user = uni.getStorageSync('user');
            const needLogin = needLoginForWechat();

            results.push(`<div class="test-result info">当前浏览器: ${navigator.userAgent}</div>`);
            results.push(`<div class="test-result ${isWechat ? 'success' : 'warning'}">是否微信浏览器: ${isWechat ? '是' : '否'}</div>`);
            results.push(`<div class="test-result info">当前用户信息: ${user || '无'}</div>`);
            results.push(`<div class="test-result ${needLogin ? 'warning' : 'success'}">需要登录控制: ${needLogin ? '是' : '否'}</div>`);

            resultDiv.innerHTML = results.join('');
        }

        function simulateLogin() {
            uni.setStorageSync('user', JSON.stringify({
                id: 123,
                name: '测试用户',
                token: 'test_token_123'
            }));
            
            document.getElementById('loginResult').innerHTML = 
                '<div class="test-result success">✓ 已模拟登录状态</div>';
        }

        function simulateLogout() {
            uni.removeStorageSync('user');
            uni.removeStorageSync('token');
            
            document.getElementById('loginResult').innerHTML = 
                '<div class="test-result warning">⚠ 已模拟未登录状态</div>';
        }

        function testNeedLoginFunction() {
            const resultDiv = document.getElementById('functionResult');
            let results = [];

            const isWechat = checkWenxin();
            const user = uni.getStorageSync('user');
            const needLogin = needLoginForWechat();

            results.push(`<div class="test-result info">checkWenxin(): ${isWechat}</div>`);
            results.push(`<div class="test-result info">用户信息: ${user || '无'}</div>`);
            results.push(`<div class="test-result info">checkNull(user): ${checkNull(user)}</div>`);
            results.push(`<div class="test-result ${needLogin ? 'warning' : 'success'}">needLoginForWechat(): ${needLogin}</div>`);

            if (needLogin) {
                results.push('<div class="test-result warning">⚠ 在微信浏览器中且未登录，接口应该被阻止</div>');
            } else {
                results.push('<div class="test-result success">✓ 可以正常调用接口</div>');
            }

            resultDiv.innerHTML = results.join('');
        }

        function startNetworkMonitor() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            networkLog = [];
            
            // 监控fetch请求
            window.fetch = function(...args) {
                const url = args[0];
                const timestamp = new Date().toLocaleTimeString();
                
                if (typeof url === 'string' && (
                    url.includes('/api/supplier/getSupplierApplyStatus') ||
                    url.includes('/api/smallShop/center/showCenter') ||
                    url.includes('/api/agent/setting/get') ||
                    url.includes('/api/center/index') ||
                    url.includes('/api/center/getMustTools')
                )) {
                    networkLog.push(`[${timestamp}] FETCH: ${url}`);
                    updateNetworkLog();
                }
                
                return originalFetch.apply(this, args);
            };

            // 监控XMLHttpRequest
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                
                xhr.open = function(method, url, ...args) {
                    const timestamp = new Date().toLocaleTimeString();
                    
                    if (typeof url === 'string' && (
                        url.includes('/api/supplier/getSupplierApplyStatus') ||
                        url.includes('/api/smallShop/center/showCenter') ||
                        url.includes('/api/agent/setting/get') ||
                        url.includes('/api/center/index') ||
                        url.includes('/api/center/getMustTools')
                    )) {
                        networkLog.push(`[${timestamp}] XHR: ${method} ${url}`);
                        updateNetworkLog();
                    }
                    
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                return xhr;
            };

            document.getElementById('networkResult').innerHTML = 
                '<div class="test-result success">✓ 网络监控已启动</div>';
        }

        function stopNetworkMonitor() {
            if (!isMonitoring) return;
            
            isMonitoring = false;
            window.fetch = originalFetch;
            window.XMLHttpRequest = originalXHR;
            
            document.getElementById('networkResult').innerHTML = 
                '<div class="test-result info">ℹ 网络监控已停止</div>';
        }

        function clearNetworkLog() {
            networkLog = [];
            updateNetworkLog();
        }

        function updateNetworkLog() {
            const logDiv = document.getElementById('networkLog');
            if (networkLog.length === 0) {
                logDiv.textContent = '暂无网络请求记录';
            } else {
                logDiv.textContent = networkLog.join('\n');
            }
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            checkEnvironment();
            updateNetworkLog();
            
            console.log('微信浏览器接口控制测试页面已加载');
            console.log('使用说明:');
            console.log('1. 首先检测当前环境');
            console.log('2. 模拟不同的登录状态');
            console.log('3. 测试needLoginForWechat函数');
            console.log('4. 开启网络监控，然后访问会员中心页面');
            console.log('5. 观察是否有受控接口被调用');
        };
    </script>
</body>
</html>
