# 微信公众号登录Code重复使用问题修复

## 问题描述
原有的`isWeiXinBrowser`函数存在微信授权码(code)重复使用的问题，导致登录时出现`GetUserAccessToken error: errcode=40163`错误。

## 问题原因
1. 微信授权码只能使用一次，使用后即失效
2. 原代码没有检测code是否已经使用过
3. 页面刷新或重复调用时会尝试使用相同的code
4. 没有正确清理URL中的授权参数

## 修复方案

### 1. 添加授权码管理函数
```javascript
// 微信授权码管理函数
Vue.prototype.clearWeChatCode = function() {
  // 清除URL中的微信授权相关参数
  if (window.location.href.includes('code') || window.location.href.includes('state')) {
    let urlParams = new URLSearchParams(window.location.search)
    urlParams.delete('code')
    urlParams.delete('state')
    const newUrl = window.location.protocol + '//' + window.location.host + window.location.pathname + 
                   (urlParams.toString() ? '?' + urlParams.toString() : '') + window.location.hash
    window.history.replaceState({}, document.title, newUrl)
  }
  // 清除本地存储的已使用code标记
  uni.removeStorageSync('wechat_used_code')
}
```

### 2. 增加登录状态检测
在`isWeiXinBrowser`函数开始处添加已登录检测：
```javascript
// 如果已经登录，直接返回，避免重复授权
const existingToken = uni.getStorageSync('token')
if (existingToken) {
  console.log('用户已登录，跳过微信授权')
  that.clearWeChatCode()
  return
}
```

### 3. 添加Code重复使用检测
```javascript
// 检查code是否已经使用过，防止重复使用导致登录报错
const usedCode = uni.getStorageSync('wechat_used_code')
if (codeValue && usedCode === codeValue) {
  // 如果code已经使用过，清除URL中的code参数并重新获取授权
  console.log('WeChat code已使用过，重新获取授权')
  that.clearWeChatCode()
  // 重新发起授权请求
  url = '/api/wechatofficial/wxLogin?url=' + pageHref.replace('#', '%23')
  that.get(url, {}, true).then(res => {
    const data = res.data
    if (data.code == 201) {
      // 还未获取code，跳转到授权页面
      window.location.href = data.redirect_url
    }
  })
  return
}
```

### 4. 标记Code为已使用
```javascript
if (codeValue) {
  // 标记当前code为已使用
  uni.setStorageSync('wechat_used_code', codeValue)
  url = '/api/wechatofficial/wxLogin?url=' + pageHref.replace('#', '%23') + '&code=' + codeValue
} else {
  url = '/api/wechatofficial/wxLogin?url=' + pageHref.replace('#', '%23')
}
```

### 5. 增强错误处理
```javascript
} else {
  // 处理其他错误情况，如code重复使用错误
  if (data.code == 40163 || (data.msg && data.msg.includes('40163'))) {
    console.log('WeChat授权码已使用，清除并重新授权')
    that.clearWeChatCode()
    // 重新发起授权
    setTimeout(() => {
      that.isWeiXinBrowser()
    }, 1000)
  } else {
    uni.showToast({
      title: data.msg || '登录失败',
      icon: 'none',
      duration: 2000,
    })
  }
}
}).catch(error => {
  console.error('WeChat login error:', error)
  // 网络错误时也清除code标记，避免卡死
  that.clearWeChatCode()
  uni.showToast({
    title: '网络错误，请重试',
    icon: 'none',
    duration: 2000,
  })
})
```

### 6. 登录成功后清理
```javascript
} else if (data.code == 0) {
  // 登录成功
  that.clearWeChatCode()
  uni.setStorageSync('token', data.token)
  uni.setStorageSync('user', data.user)
  uni.setStorageSync('type', 'refresh')
  // ... 其他登录成功逻辑
}
```

## 修复效果

1. **防止Code重复使用**: 通过本地存储标记已使用的code，避免重复使用
2. **自动清理URL参数**: 使用`history.replaceState`清理URL中的授权参数，避免页面刷新时重复使用
3. **智能重新授权**: 检测到code已使用时自动重新发起授权流程
4. **增强错误处理**: 针对40163错误码进行特殊处理
5. **避免重复登录**: 检测已登录状态，避免不必要的授权流程

## 测试方法

1. 打开`test-wechat-login.html`进行功能测试
2. 在微信浏览器中测试实际登录流程
3. 模拟页面刷新和重复调用场景

## 注意事项

1. 此修复适用于微信公众号网页授权登录
2. 需要确保服务端也正确处理授权码的一次性使用
3. 建议在生产环境中进行充分测试
4. 可以根据实际需求调整重新授权的延迟时间

## 相关文件

- `src/main.js`: 主要修复文件
- `test-wechat-login.html`: 测试页面
- `WECHAT_LOGIN_FIX.md`: 本说明文档
