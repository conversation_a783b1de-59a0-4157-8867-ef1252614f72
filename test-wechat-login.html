<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>微信公众号登录测试</h1>
    <p>此页面用于测试微信公众号登录功能中的code重复使用问题修复。</p>

    <div class="test-section">
        <h2>1. 模拟微信授权码管理</h2>
        <button onclick="testCodeManagement()">测试授权码管理</button>
        <div id="codeTestResult"></div>
    </div>

    <div class="test-section">
        <h2>2. 模拟URL参数清理</h2>
        <button onclick="testUrlCleaning()">测试URL清理</button>
        <div id="urlTestResult"></div>
    </div>

    <div class="test-section">
        <h2>3. 模拟重复登录检测</h2>
        <button onclick="testDuplicateLogin()">测试重复登录检测</button>
        <div id="duplicateTestResult"></div>
    </div>

    <div class="test-section">
        <h2>4. 当前URL状态</h2>
        <div class="code-display" id="currentUrl"></div>
        <button onclick="addTestCode()">添加测试code参数</button>
        <button onclick="clearTestCode()">清除code参数</button>
    </div>

    <script>
        // 模拟uni对象
        const uni = {
            storage: {},
            getStorageSync: function(key) {
                return this.storage[key] || '';
            },
            setStorageSync: function(key, value) {
                this.storage[key] = value;
            },
            removeStorageSync: function(key) {
                delete this.storage[key];
            },
            showToast: function(options) {
                console.log('Toast:', options.title);
            }
        };

        // 模拟clearWeChatCode函数
        function clearWeChatCode() {
            // 清除URL中的微信授权相关参数
            if (window.location.href.includes('code') || window.location.href.includes('state')) {
                let urlParams = new URLSearchParams(window.location.search);
                urlParams.delete('code');
                urlParams.delete('state');
                const newUrl = window.location.protocol + '//' + window.location.host + window.location.pathname + 
                               (urlParams.toString() ? '?' + urlParams.toString() : '') + window.location.hash;
                window.history.replaceState({}, document.title, newUrl);
            }
            // 清除本地存储的已使用code标记
            uni.removeStorageSync('wechat_used_code');
        }

        function testCodeManagement() {
            const resultDiv = document.getElementById('codeTestResult');
            let results = [];

            // 测试1: 设置和检测已使用的code
            const testCode = 'test_code_12345';
            uni.setStorageSync('wechat_used_code', testCode);
            const storedCode = uni.getStorageSync('wechat_used_code');
            
            if (storedCode === testCode) {
                results.push('<div class="test-result success">✓ 授权码存储功能正常</div>');
            } else {
                results.push('<div class="test-result error">✗ 授权码存储功能异常</div>');
            }

            // 测试2: 清除已使用的code
            clearWeChatCode();
            const clearedCode = uni.getStorageSync('wechat_used_code');
            
            if (clearedCode === '') {
                results.push('<div class="test-result success">✓ 授权码清除功能正常</div>');
            } else {
                results.push('<div class="test-result error">✗ 授权码清除功能异常</div>');
            }

            resultDiv.innerHTML = results.join('');
        }

        function testUrlCleaning() {
            const resultDiv = document.getElementById('urlTestResult');
            let results = [];

            // 获取当前URL状态
            const originalUrl = window.location.href;
            const hasCodeBefore = originalUrl.includes('code=');
            
            results.push(`<div class="test-result info">原始URL: ${originalUrl}</div>`);

            // 执行清理
            clearWeChatCode();
            
            const cleanedUrl = window.location.href;
            const hasCodeAfter = cleanedUrl.includes('code=');
            
            results.push(`<div class="test-result info">清理后URL: ${cleanedUrl}</div>`);

            if (hasCodeBefore && !hasCodeAfter) {
                results.push('<div class="test-result success">✓ URL清理功能正常 - 成功移除code参数</div>');
            } else if (!hasCodeBefore && !hasCodeAfter) {
                results.push('<div class="test-result info">ℹ URL清理功能正常 - 原本就没有code参数</div>');
            } else {
                results.push('<div class="test-result error">✗ URL清理功能异常 - 未能移除code参数</div>');
            }

            resultDiv.innerHTML = results.join('');
            updateCurrentUrl();
        }

        function testDuplicateLogin() {
            const resultDiv = document.getElementById('duplicateTestResult');
            let results = [];

            // 模拟已登录状态
            uni.setStorageSync('token', 'test_token_12345');
            const token = uni.getStorageSync('token');
            
            if (token) {
                results.push('<div class="test-result success">✓ 登录状态检测正常 - 检测到已有token</div>');
                results.push('<div class="test-result info">ℹ 在实际应用中，此时会跳过微信授权流程</div>');
            } else {
                results.push('<div class="test-result error">✗ 登录状态检测异常</div>');
            }

            // 清除登录状态
            uni.removeStorageSync('token');
            const clearedToken = uni.getStorageSync('token');
            
            if (!clearedToken) {
                results.push('<div class="test-result success">✓ 登录状态清除正常</div>');
            } else {
                results.push('<div class="test-result error">✗ 登录状态清除异常</div>');
            }

            resultDiv.innerHTML = results.join('');
        }

        function addTestCode() {
            const url = new URL(window.location);
            url.searchParams.set('code', 'test_wechat_code_12345');
            url.searchParams.set('state', 'test_state');
            window.history.replaceState({}, document.title, url.toString());
            updateCurrentUrl();
        }

        function clearTestCode() {
            clearWeChatCode();
            updateCurrentUrl();
        }

        function updateCurrentUrl() {
            document.getElementById('currentUrl').textContent = window.location.href;
        }

        // 初始化显示当前URL
        updateCurrentUrl();

        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('微信登录测试页面已加载');
            console.log('主要修复内容:');
            console.log('1. 防止微信授权码重复使用');
            console.log('2. 自动清理URL中的授权参数');
            console.log('3. 检测已登录状态避免重复授权');
            console.log('4. 增强错误处理机制');
        };
    </script>
</body>
</html>
